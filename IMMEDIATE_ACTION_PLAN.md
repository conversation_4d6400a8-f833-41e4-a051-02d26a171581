# 🚨 План немедленных действий для earnlyze.me

## 📋 Критические задачи для выполнения СЕГОДНЯ

### 🔥 Приоритет 1: Исправление CCXT для фьючерсной торговли

**Проблема:** Текущая настройка CCXT неправильно сконфигурирована для торговли фьючерсами.

**Решение:**
1. **Обновить `backend/app/core/exchange_manager.py`:**
   ```python
   # Заменить текущие настройки на:
   'binance': {
       'apiKey': settings.BINANCE_API_KEY,
       'secret': settings.BINANCE_SECRET,
       'sandbox': settings.BINANCE_SANDBOX,
       'options': {
           'defaultType': 'swap',  # Для perpetual futures
           'marginMode': 'isolated',
       }
   },
   'bybit': {
       'apiKey': settings.BYBIT_API_KEY,
       'secret': settings.BYBIT_SECRET,
       'sandbox': settings.BYBIT_SANDBOX,
       'options': {
           'defaultType': 'linear',  # Для линейных фьючерсов
           'marginMode': 'isolated',
       }
   }
   ```

2. **Добавить методы для работы с leverage:**
   ```python
   async def set_leverage(self, exchange_name: str, symbol: str, leverage: int):
       exchange = await self.get_exchange(exchange_name)
       return await exchange.set_leverage(leverage, symbol)
   
   async def set_margin_mode(self, exchange_name: str, symbol: str, mode: str):
       exchange = await self.get_exchange(exchange_name)
       return await exchange.set_margin_mode(symbol, mode)
   ```

**Время выполнения:** 2-3 часа

### 🔐 Приоритет 2: Шифрование API ключей

**Проблема:** API ключи бирж хранятся в открытом виде в базе данных.

**Решение:**
1. **Создать `backend/app/services/encryption_service.py`:**
   ```python
   from cryptography.fernet import Fernet
   import os
   import base64
   
   class EncryptionService:
       def __init__(self):
           key = os.getenv('ENCRYPTION_KEY')
           if not key:
               key = Fernet.generate_key()
               print(f"Generated new encryption key: {key.decode()}")
           else:
               key = key.encode()
           self.cipher = Fernet(key)
       
       def encrypt(self, data: str) -> str:
           return self.cipher.encrypt(data.encode()).decode()
       
       def decrypt(self, encrypted_data: str) -> str:
           return self.cipher.decrypt(encrypted_data.encode()).decode()
   ```

2. **Обновить модель ExchangeKey:**
   ```python
   # Добавить методы в backend/app/models/exchange_key.py
   def set_api_credentials(self, api_key: str, secret: str, passphrase: str = None):
       encryption_service = EncryptionService()
       self.api_key_encrypted = encryption_service.encrypt(api_key)
       self.secret_encrypted = encryption_service.encrypt(secret)
       if passphrase:
           self.passphrase_encrypted = encryption_service.encrypt(passphrase)
   
   def get_api_credentials(self) -> dict:
       encryption_service = EncryptionService()
       return {
           'api_key': encryption_service.decrypt(self.api_key_encrypted),
           'secret': encryption_service.decrypt(self.secret_encrypted),
           'passphrase': encryption_service.decrypt(self.passphrase_encrypted) if self.passphrase_encrypted else None
       }
   ```

**Время выполнения:** 3-4 часа

### ⚙️ Приоритет 3: Создание формы для ботов в Frontend

**Проблема:** Пользователи не могут создавать торговых ботов через интерфейс.

**Решение:**
1. **Создать `frontend/src/components/BotCreationForm.tsx`:**
   ```typescript
   import React, { useState } from 'react';
   import { 
     Dialog, DialogTitle, DialogContent, DialogActions,
     TextField, Select, MenuItem, Button, Grid, FormControl, InputLabel
   } from '@mui/material';
   
   interface BotCreationFormProps {
     open: boolean;
     onClose: () => void;
     onSubmit: (botData: any) => void;
   }
   
   export const BotCreationForm: React.FC<BotCreationFormProps> = ({ open, onClose, onSubmit }) => {
     const [formData, setFormData] = useState({
       name: '',
       exchange: '',
       symbol: '',
       grid_count: 10,
       grid_step_percent: 1.0,
       deposit_amount: 100,
       leverage: 1,
       tp_percent: 5.0,
       sl_percent: 10.0,
       exchange_key_id: ''
     });
   
     const handleSubmit = () => {
       onSubmit(formData);
       onClose();
     };
   
     return (
       <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
         <DialogTitle>Создать нового бота</DialogTitle>
         <DialogContent>
           <Grid container spacing={2} sx={{ mt: 1 }}>
             <Grid item xs={12}>
               <TextField
                 fullWidth
                 label="Название бота"
                 value={formData.name}
                 onChange={(e) => setFormData({...formData, name: e.target.value})}
               />
             </Grid>
             <Grid item xs={6}>
               <FormControl fullWidth>
                 <InputLabel>Биржа</InputLabel>
                 <Select
                   value={formData.exchange}
                   onChange={(e) => setFormData({...formData, exchange: e.target.value})}
                 >
                   <MenuItem value="binance">Binance</MenuItem>
                   <MenuItem value="bybit">Bybit</MenuItem>
                 </Select>
               </FormControl>
             </Grid>
             <Grid item xs={6}>
               <TextField
                 fullWidth
                 label="Торговая пара"
                 value={formData.symbol}
                 onChange={(e) => setFormData({...formData, symbol: e.target.value})}
                 placeholder="BTC/USDT:USDT"
               />
             </Grid>
             {/* Добавить остальные поля */}
           </Grid>
         </DialogContent>
         <DialogActions>
           <Button onClick={onClose}>Отмена</Button>
           <Button onClick={handleSubmit} variant="contained">Создать</Button>
         </DialogActions>
       </Dialog>
     );
   };
   ```

2. **Интегрировать в BotsPage:**
   ```typescript
   // В frontend/src/pages/BotsPage.tsx добавить:
   const [createDialogOpen, setCreateDialogOpen] = useState(false);
   
   const handleCreateBot = async (botData: any) => {
     try {
       await api.post('/bots/', botData);
       // Обновить список ботов
     } catch (error) {
       console.error('Ошибка создания бота:', error);
     }
   };
   ```

**Время выполнения:** 4-5 часов

### 🔧 Приоритет 4: Доработка API эндпоинта создания ботов

**Проблема:** API эндпоинт для создания ботов не полностью функционален.

**Решение:**
1. **Обновить `backend/app/api/v1/bots.py`:**
   ```python
   @router.post("/", response_model=BotResponse)
   async def create_bot(
       bot_data: BotCreate,
       current_user: User = Depends(get_current_active_user),
       db: Session = Depends(get_db)
   ):
       """Создать нового торгового бота."""
       try:
           # Проверить существование API ключа
           exchange_key = db.query(ExchangeKey).filter(
               ExchangeKey.id == bot_data.exchange_key_id,
               ExchangeKey.user_id == current_user.id
           ).first()
           
           if not exchange_key:
               raise HTTPException(
                   status_code=status.HTTP_404_NOT_FOUND,
                   detail="API ключ не найден"
               )
           
           # Проверить баланс на бирже
           balance = await exchange_service.get_balance(exchange_key.id)
           if balance.get('error'):
               raise HTTPException(
                   status_code=status.HTTP_400_BAD_REQUEST,
                   detail="Не удалось получить баланс с биржи"
               )
           
           # Создать бота
           bot = GridBot(
               name=bot_data.name,
               user_id=current_user.id,
               exchange_key_id=bot_data.exchange_key_id,
               symbol=bot_data.symbol,
               grid_count=bot_data.grid_count,
               grid_step_percent=bot_data.grid_step_percent,
               deposit_amount=bot_data.deposit_amount,
               leverage=bot_data.leverage,
               tp_percent=bot_data.tp_percent,
               sl_percent=bot_data.sl_percent
           )
           
           db.add(bot)
           db.commit()
           db.refresh(bot)
           
           logger.info(f"Создан новый бот: {bot.name} для пользователя {current_user.username}")
           return bot
           
       except HTTPException:
           raise
       except Exception as e:
           logger.error(f"Ошибка создания бота: {e}")
           raise HTTPException(
               status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
               detail="Ошибка создания бота"
           )
   ```

**Время выполнения:** 2-3 часа

## 📊 План на первую неделю

### День 1 (Сегодня)
- [x] Анализ текущего состояния проекта ✅
- [ ] Исправление CCXT настроек для фьючерсов
- [ ] Начало работы над шифрованием API ключей

### День 2
- [ ] Завершение системы шифрования
- [ ] Создание формы создания ботов
- [ ] Тестирование CCXT с testnet

### День 3
- [ ] Доработка API эндпоинтов для ботов
- [ ] Интеграция формы с backend
- [ ] Тестирование создания ботов

### День 4-5
- [ ] Доработка торгового движка
- [ ] Реальное размещение ордеров
- [ ] Мониторинг исполнения

### День 6-7
- [ ] Тестирование всей системы
- [ ] Исправление найденных ошибок
- [ ] Подготовка к следующему этапу

## 🎯 Критерии успеха первой недели

**Технические:**
- ✅ CCXT правильно работает с фьючерсами
- ✅ API ключи зашифрованы в БД
- ✅ Пользователи могут создавать ботов через UI
- ✅ Боты могут размещать тестовые ордера

**Пользовательские:**
- ✅ Форма создания бота интуитивно понятна
- ✅ Процесс создания занимает < 2 минут
- ✅ Ошибки отображаются понятно

**Безопасность:**
- ✅ API ключи не видны в логах
- ✅ Шифрование работает корректно
- ✅ Валидация входных данных

## 🚀 Следующие шаги

После завершения критических задач:
1. **WebSocket для real-time обновлений**
2. **Email сервис для уведомлений**
3. **Telegram бот интеграция**
4. **Графики и аналитика**

**Цель:** К концу первой недели иметь функционирующую базовую версию платформы, где пользователи могут безопасно создавать и запускать торговых ботов на testnet биржах.

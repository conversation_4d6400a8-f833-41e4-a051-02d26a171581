"""
API эндпоинты для аутентификации
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from fastapi import APIRouter, HTTPException, status, Depends, Request
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_async_db
from app.models.user import User, UserRole
from app.schemas.user import (
    UserLogin, UserRegister, TokenResponse, UserResponse, UserUpdate,
    EmailVerificationRequest, PasswordResetRequest, PasswordResetConfirm,
    OTPSetupResponse, OTPVerifyRequest, OTPDisableRequest, BackupCodesResponse
)
from app.services.user_service import UserService
from app.services.email_service import EmailService
from app.services.jwt_service import jwt_service

security = HTTPBearer()
router = APIRouter()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_db)
) -> User:
    """Получить текущего пользователя по токену"""
    # Проверить токен через jwt_service
    payload = jwt_service.verify_token(credentials.credentials)
    user_id = payload.get("sub")

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Неверный токен",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Получить пользователя из БД
    user_service = UserService(db)
    user = await user_service.get_user_by_id(int(user_id))

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Пользователь не найден",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Получить текущего активного пользователя"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Неактивный пользователь"
        )
    return current_user


async def get_admin_user(current_user: User = Depends(get_current_active_user)) -> User:
    """Получить текущего пользователя с правами администратора"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Недостаточно прав"
        )
    return current_user


@router.post("/register")
async def register(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_async_db)
):
    """Регистрация нового пользователя"""
    try:
        # Простая проверка уникальности
        from sqlalchemy import select
        from app.models.user import User

        result = await db.execute(
            select(User).where(
                (User.username == user_data.username) | (User.email == user_data.email)
            )
        )
        existing_user = result.scalars().first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Пользователь с таким именем или email уже существует"
            )

        # Создать пользователя
        from app.models.user import UserStatus
        user = User(
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name,
            status=UserStatus.ACTIVE,  # Сразу активируем для демо
            is_email_verified=True     # Сразу подтверждаем email для демо
        )
        user.set_password(user_data.password)

        db.add(user)
        await db.commit()
        await db.refresh(user)

        return {"message": "Пользователь успешно зарегистрирован", "user_id": user.id}
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при регистрации пользователя: {str(e)}"
        )


@router.post("/login")
async def login(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_async_db)
):
    """Вход в систему"""
    try:
        from sqlalchemy import select, or_
        from app.models.user import User, UserStatus
        from datetime import datetime

        # Найти пользователя по username или email
        result = await db.execute(
            select(User).where(
                or_(User.username == login_data.username, User.email == login_data.username)
            )
        )
        user = result.scalars().first()

        if not user or not user.verify_password(login_data.password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Неверный логин или пароль",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Проверить статус пользователя
        if user.status != UserStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Аккаунт не активен"
            )

        # Создать JWT токен
        token_data = {
            "sub": str(user.id),
            "username": user.username,
            "email": user.email,
            "role": user.role.value
        }
        access_token = jwt_service.create_access_token(token_data)

        # Обновить время последнего входа
        user.last_login = datetime.utcnow()
        await db.commit()

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": jwt_service.access_token_expire_minutes * 60,
            "user": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "role": user.role.value,
                "status": user.status.value,
                "is_email_verified": user.is_email_verified
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при входе в систему: {str(e)}"
        )


@router.get("/me", response_model=UserResponse)
async def get_me(current_user: User = Depends(get_current_active_user)):
    """Получить информацию о текущем пользователе"""
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_me(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Обновить информацию о текущем пользователе"""
    try:
        # Обновить поля пользователя
        if user_data.full_name is not None:
            current_user.full_name = user_data.full_name

        if user_data.email is not None and user_data.email != current_user.email:
            # Проверить уникальность email
            from sqlalchemy import select
            result = await db.execute(
                select(User).where(User.email == user_data.email)
            )
            existing_user = result.scalars().first()
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Пользователь с таким email уже существует"
                )
            current_user.email = user_data.email
            current_user.is_email_verified = False  # Требуется повторная верификация

        if user_data.password is not None:
            current_user.set_password(user_data.password)

        await db.commit()
        await db.refresh(current_user)

        return current_user
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при обновлении профиля: {str(e)}"
        )


@router.post("/verify-email")
async def verify_email(
    verification_data: EmailVerificationRequest,
    db: AsyncSession = Depends(get_async_db)
):
    """Подтвердить email адрес"""
    user_service = UserService(db)

    success = await user_service.verify_email(verification_data.token)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Неверный или истекший токен"
        )

    return {"message": "Email успешно подтвержден"}


@router.post("/request-password-reset")
async def request_password_reset(
    reset_data: PasswordResetRequest,
    db: AsyncSession = Depends(get_async_db)
):
    """Запросить сброс пароля"""
    user_service = UserService(db)
    email_service = EmailService()

    user = await user_service.get_user_by_email(reset_data.email)
    if user:
        token = user.generate_password_reset_token()
        await db.commit()

        await email_service.send_password_reset_email(
            user.email, token, user.username
        )

    # Всегда возвращаем успех для безопасности
    return {"message": "Если email существует, инструкции отправлены"}


@router.post("/logout")
async def logout():
    """Выход из системы"""
    return {"message": "Выход выполнен успешно"}


@router.post("/setup-otp")
async def setup_otp(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Настроить OTP для пользователя"""
    try:
        if current_user.is_otp_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="OTP уже включен"
            )

        # Настроить OTP
        secret, qr_code = current_user.setup_otp()
        backup_codes = current_user.generate_backup_codes()

        await db.commit()

        return {
            "secret": secret,
            "qr_code": qr_code,
            "backup_codes": backup_codes,
            "message": "OTP настроен. Подтвердите с помощью /verify-otp для активации."
        }
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при настройке OTP: {str(e)}"
        )


@router.post("/verify-otp")
async def verify_otp(
    otp_data: OTPVerifyRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Подтвердить и активировать OTP"""
    try:
        if current_user.is_otp_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="OTP уже активен"
            )

        if not current_user.otp_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="OTP не настроен. Сначала вызовите /setup-otp"
            )

        # Проверить OTP токен
        if not current_user.verify_otp(otp_data.token):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Неверный OTP токен"
            )

        # Активировать OTP
        current_user.is_otp_enabled = True
        await db.commit()

        return {"message": "OTP успешно активирован"}
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при подтверждении OTP: {str(e)}"
        )


@router.post("/disable-otp")
async def disable_otp(
    otp_data: OTPDisableRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Отключить OTP"""
    try:
        if not current_user.is_otp_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="OTP не активен"
            )

        # Проверить пароль
        if not current_user.verify_password(otp_data.password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Неверный пароль"
            )

        # Проверить OTP токен или резервный код
        if otp_data.token:
            if not (current_user.verify_otp(otp_data.token) or
                    current_user.verify_backup_code(otp_data.token)):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Неверный OTP токен или резервный код"
                )

        # Отключить OTP
        current_user.is_otp_enabled = False
        current_user.otp_secret = None
        current_user.backup_codes = None

        await db.commit()

        return {"message": "OTP успешно отключен"}
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при отключении OTP: {str(e)}"
        )


@router.get("/backup-codes")
async def get_backup_codes(
    current_user: User = Depends(get_current_active_user)
):
    """Получить резервные коды"""
    try:
        if not current_user.is_otp_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="OTP не активен"
            )

        codes = current_user.get_backup_codes()
        return {"codes": codes}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при получении резервных кодов: {str(e)}"
        )
